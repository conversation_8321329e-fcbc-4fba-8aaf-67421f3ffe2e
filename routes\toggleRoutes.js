const express = require('express');
const router = express.Router();
const apiStatus = require('../utils/apiControl');

// POST /toggle - { status: true/false }
/**
 * @swagger
 * /control/toggle:
 *   post:
 *     summary: Toggle all APIs
 *     tags: [Toggle]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: boolean
 *                 description: The status of the APIs
 *     responses:
 *       200:
 *         description: APIs toggled successfully
 *       400:
 *         description: Bad request
 *       500:
 *         description: Internal server error
 */
router.post('/toggle', (req, res) => {
  const { status } = req.body;

  if (typeof status !== 'boolean') {
    return res.status(400).json({ message: 'Status must be boolean (true or false)' });
  }

  apiStatus.allAPIs = status;
  return res.json({ message: `All APIs are now ${status ? 'ON' : 'OFF'}` });
});

module.exports = router;
